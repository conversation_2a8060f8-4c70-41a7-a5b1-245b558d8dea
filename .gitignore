# Visual Studio 2015 user specific files
.vs/

Binaries/

# Compiled Object files
*.slo
*.lo
*.o
*.obj

# Don't commit PDB files for now because they take too much space
*.pdb

# Precompiled Headers
*.gch
*.pch

# Executables
*.exe
*.out
*.app
*.ipa

# These project files can be generated by the engine
*.xcodeproj
*.xcworkspace
*.sln
*.suo
*.opensdf
*.sdf
*.VC.db
*.VC.opendb

# Precompiled Assets
SourceArt/**/*.png
SourceArt/**/*.tga

# Binary Files
# Binaries/*
Plugins/*/Binaries/*

# Builds
Build/*
CMakeLists.txt

# Whitelist PakBlacklist-<BuildConfiguration>.txt files
!Build/*/
Build/*/**
!Build/*/PakBlacklist*.txt

# Don't ignore icon files in Build
!Build/**/*.ico

# Built data for maps
*_BuiltData.uasset

# Configuration files generated by the Editor
Plugins/*/Saved/*
Saved/*

# Compiled source files for the engine to use
Intermediate/*
Plugins/*/Intermediate/*

# Cache files for the editor to use
DerivedDataCache/*
.idea

# Wwise
*_WwiseProject/*
#Plugins/Wwise/*
Plugins/Developer/RiderLink/*
